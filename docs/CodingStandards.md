# Laravel アプリケーション開発ガイドライン
## バグを防ぐための包括的開発規約

### 現在のプロジェクト情報
- **使用バージョン**: Laravel 12.13.0
- **PHPバージョン要件**: PHP 8.3.*（8.4は非対応）
- **Node.js**: 22.x
- **Supabase CLI**: 2.22.12（固定バージョン）
- **リリース日**: 2025年2月24日
- **バグ修正サポート期間**: 2026年8月13日まで
- **セキュリティ修正サポート期間**: 2027年2月24日まで

## コーディング規約 (機械可読形式)

```yaml
# JTT-Apps コーディング標準 (v1.0.0)
version: 1.0.0
last_updated: "2025-05-12"
php: "8.3.*"
frameworks:
  laravel:
    version: "12.13.0"
    min_php: "8.3.*"
    support_end: "2027-02-24"
languages:
  php:
    version: "8.3.*"
    style: "PSR-12"
    indent: 4
    max_line_length: 120
  javascript:
    version: "ES2022"
    style: "prettier"
    indent: 2
architecture:
  pattern: "layered"
  dependencies:
    - "Controller -> Service -> Model -> DB"
  naming:
    classes: "PascalCase"
    methods: "camelCase"
    variables: "camelCase"
    constants: "SNAKE_CASE_CAPS"
    private_props: "camelCase"
    boolean_methods: "is*, has*, can*"
tools:
  testing:
    - "phpunit"
    - "larastan"
    - "playwright"
  ci:
    - "GitHub Actions"
  local:
    - "supabase-cli 2.22.12"

## アーキテクチャガイド
当プロジェクトでは、レイヤードアーキテクチャを採用しています。詳細は以下のドキュメントを参照してください：
- [レイヤードアーキテクチャマニュアル](./laravel_manual/controller_service_model_manual.html)

## 全般的なコーディング規約

### 基本原則
1. **シンプルさを重視する** - 複雑さよりも明快さを優先する
2. **DRY原則（Don't Repeat Yourself）** - コードの重複を避ける
3. **SOLID原則に準拠する** - 特に単一責任の原則を意識する
4. **明示的なコード** - 暗黙的なマジックよりも明示的な実装を優先する
5. **一貫性** - 既存のコードスタイルに合わせる

### 命名規則
- **クラス名**: PascalCase (`UserService`, `OrderController`)
- **メソッド名**: camelCase (`createUser()`, `findById()`)
- **変数名**: camelCase (`$userName`, `$orderTotal`)
- **定数**: SNAKE_CASE_CAPS (`APP_ENV`, `USER_ROLES`)
- **プライベートプロパティ**: camelCaseで先頭にアンダースコアを付けない (`$userRepository`)
- **ブール値を返すメソッド**: `is`, `has`, `can`などの接頭辞を使用 (`isValid()`, `hasPermission()`)

### ファイル構造
- 1ファイル1クラスの原則を守る
- ファイル名はクラス名と一致させる
- 名前空間はPSR-4オートローディングに準拠する

### コードスタイル
- PSR-12に準拠する
- インデントはスペース4つを使用
- 行の最大長は120文字まで
- 名前空間の宣言後は1行空ける
- use宣言のグループ間は1行空ける
- メソッド間は1行空ける
- 演算子の前後にスペースを入れる
- 制御構造キーワード後にスペースを入れる (`if (condition)`)

### PHPDoc
- 公開メソッドには必ずPHPDocを付ける
- パラメータと戻り値の型を明示する
- 複雑なロジックには説明コメントを追加する

```php
/**
 * ユーザーを作成し、ウェルカムメールを送信する
 *
 * @param array $userData ユーザーデータ
 * @param bool $sendEmail メール送信フラグ
 * @return User 作成されたユーザーエンティティ
 * @throws ValidationException バリデーションエラー時
 */
public function createUser(array $userData, bool $sendEmail = true): User
{
    // 実装
}
- 依存性注入コンテナがクラスプロパティのデフォルト値を尊重
- UUIDはデフォルトでUUIDv7（時間ベース）を使用
- マルチスキーマデータベースのサポート強化
- SVG画像はセキュリティ上の理由でデフォルトで無効化
- リポジトリパターンは必須ではないが、大規模アプリケーションでは有用

## リポジトリパターンについて
- 小〜中規模のアプリケーションではEloquentの機能のみで十分な場合が多い
- インターフェースを使用して実装を分離することでテスト容易性を高めることを推奨
- 単一責任の原則に従った小さなサービスクラスの使用を推奨

## 公式ドキュメント
最新の情報は公式ドキュメントを参照してください：
https://laravel.com/docs/12.x
