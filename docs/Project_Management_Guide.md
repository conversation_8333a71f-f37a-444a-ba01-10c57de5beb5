# プロジェクト管理ガイド
## JTT-Apps プロジェクトの効率的な管理方法

### 概要
このガイドでは、JTT-Appsプロジェクトの効率的な管理方法、Issue管理、タスク優先順位付け、チーム連携について説明します。

## プロジェクト構造

### ディレクトリ構成
```
jtt-apps/
├── AGENTS.md                    # AIエージェント向けガイド（ルート必須）
├── CLAUDE.md                    # Claude向けガイド（ルート必須）
├── CONTRIBUTING.md              # 貢献ガイド（ルート）
├── README.md                    # プロジェクト概要（ルート）
├── app/                         # Laravelアプリケーション
│   ├── Http/Controllers/        # コントローラー
│   ├── Services/               # ビジネスロジック
│   ├── Models/                 # Eloquentモデル
│   └── Livewire/               # Livewireコンポーネント
├── docs/                       # プロジェクトドキュメント
│   ├── Development_Workflow_Guide.md
│   ├── CodingStandards.md
│   ├── View_Implementation_Guidelines.md
│   ├── TDD_Guide.md
│   ├── Security_Guidelines.md
│   └── laravel_manual/         # 詳細マニュアル
├── tests/                      # テストファイル
│   ├── Feature/                # 機能テスト
│   └── Unit/                   # 単体テスト
└── supabase/                   # Supabaseマイグレーション
    └── migrations/
```

## Issue管理

### Issue分類システム

#### ラベル体系
| ラベル | 説明 | 優先度 |
|--------|------|--------|
| `priority/critical` | 本番環境に影響する重大な問題 | 最高 |
| `priority/high` | 重要な機能追加・修正 | 高 |
| `priority/medium` | 通常の機能追加・改善 | 中 |
| `priority/low` | 軽微な改善・リファクタリング | 低 |
| `type/bug` | バグ修正 | - |
| `type/feature` | 新機能追加 | - |
| `type/enhancement` | 既存機能の改善 | - |
| `type/docs` | ドキュメント関連 | - |
| `type/refactor` | リファクタリング | - |
| `status/blocked` | 他の作業待ち | - |
| `status/in-progress` | 作業中 | - |
| `status/review` | レビュー待ち | - |

#### Issue作成テンプレート

##### 機能追加Issue
```markdown
## 概要
新機能の簡潔な説明

## 背景・目的
なぜこの機能が必要か

## 要件
- [ ] 要件1
- [ ] 要件2
- [ ] 要件3

## 受け入れ条件
- [ ] 条件1
- [ ] 条件2
- [ ] 条件3

## 技術的詳細
- 使用技術
- 実装方針
- 考慮事項

## 関連Issue
- #123
- #456

## 見積もり
- 工数: X日
- 難易度: 低/中/高
```

##### バグ修正Issue
```markdown
## 現象
バグの具体的な症状

## 再現手順
1. 手順1
2. 手順2
3. 手順3

## 期待する動作
正常な場合の動作

## 実際の動作
実際に起こっている動作

## 環境
- OS: 
- ブラウザ: 
- Laravel: 12.13.0
- PHP: 8.3.6

## 追加情報
- エラーログ
- スクリーンショット
- 関連する設定
```

### Issue優先順位付け

#### 優先度マトリックス
| 影響度 | 緊急度 | 優先度 | 対応時間 |
|--------|--------|--------|----------|
| 高 | 高 | Critical | 即座 |
| 高 | 低 | High | 1-2日 |
| 低 | 高 | Medium | 3-5日 |
| 低 | 低 | Low | 1-2週間 |

#### 優先度判定基準
- **Critical**: 本番環境停止、セキュリティ脆弱性、データ損失
- **High**: 主要機能の不具合、パフォーマンス問題
- **Medium**: 軽微な不具合、機能改善
- **Low**: UI改善、リファクタリング、ドキュメント更新

## スプリント管理

### スプリント期間
- **期間**: 2週間
- **開始**: 月曜日
- **終了**: 金曜日（翌週）

### スプリント構成
```
Week 1:
月: スプリント計画、Issue割り当て
火-金: 開発作業

Week 2:
月-木: 開発作業、テスト
金: レビュー、リリース準備、振り返り
```

### スプリント計画プロセス
1. **バックログ整理**: Issue優先順位の見直し
2. **工数見積もり**: 各Issueの工数算出
3. **キャパシティ確認**: チームの作業可能時間
4. **Issue割り当て**: 担当者とスケジュール決定
5. **目標設定**: スプリントゴールの明確化

## タスク管理

### GitHub Projects活用

#### ボード構成
| カラム | 説明 | 移動条件 |
|--------|------|----------|
| Backlog | 未着手のIssue | - |
| Todo | スプリントで実施予定 | スプリント計画で選択 |
| In Progress | 作業中 | 作業開始時 |
| Review | レビュー待ち | PR作成時 |
| Testing | テスト中 | レビュー承認後 |
| Done | 完了 | マージ後 |

#### 自動化ルール
- Issue作成 → Backlogに追加
- PR作成 → Reviewに移動
- PR承認 → Testingに移動
- PRマージ → Doneに移動

### 作業フロー
```mermaid
graph TD
A[Issue作成] --> B[優先度設定]
B --> C[スプリント計画]
C --> D[作業開始]
D --> E[ブランチ作成]
E --> F[TDD実装]
F --> G[PR作成]
G --> H[CI実行]
H --> I{CI成功?}
I -->|No| F
I -->|Yes| J[レビュー依頼]
J --> K[レビュー実施]
K --> L{承認?}
L -->|No| F
L -->|Yes| M[マージ]
M --> N[Issue完了]
```

## チーム連携

### コミュニケーションルール

#### 定期ミーティング
- **デイリースタンドアップ**: 毎日10分
  - 昨日の進捗
  - 今日の予定
  - ブロッカー
- **スプリントレビュー**: 2週間ごと
  - 完了した機能のデモ
  - 成果の確認
- **振り返り**: 2週間ごと
  - 良かった点
  - 改善点
  - アクションアイテム

#### 非同期コミュニケーション
- **GitHub**: Issue、PR、レビューコメント
- **ドキュメント**: 設計書、仕様書の共有
- **Slack/Discord**: 緊急時の連絡

### レビュープロセス

#### コードレビュー観点
1. **機能性**: 要件を満たしているか
2. **品質**: コーディング規約に準拠しているか
3. **セキュリティ**: セキュリティ要件を満たしているか
4. **パフォーマンス**: 性能に問題はないか
5. **テスト**: 適切なテストが書かれているか
6. **保守性**: 理解しやすく修正しやすいか

#### レビューガイドライン
- **建設的なフィードバック**: 改善提案を具体的に
- **迅速な対応**: 24時間以内にレビュー
- **学習機会**: 知識共有の場として活用

## 品質管理

### 品質メトリクス

#### コード品質
- **テストカバレッジ**: 80%以上
- **静的解析**: PHPStanレベル8でエラーなし
- **コードスタイル**: Laravel Pint準拠

#### プロセス品質
- **Issue解決時間**: 平均5日以内
- **PR承認時間**: 平均24時間以内
- **バグ再発率**: 5%以下

#### 品質チェックポイント
- [ ] 全テストが通ること
- [ ] コードスタイルチェックが通ること
- [ ] 静的解析でエラーがないこと
- [ ] セキュリティ要件を満たしていること
- [ ] パフォーマンス要件を満たしていること

### 継続的改善

#### 定期レビュー項目
- **プロセス効率**: 開発フローの見直し
- **ツール活用**: 新しいツールの導入検討
- **スキル向上**: チーム学習計画
- **品質向上**: 品質メトリクスの改善

#### 改善アクション
1. **問題の特定**: メトリクスやフィードバックから課題抽出
2. **原因分析**: 根本原因の調査
3. **対策立案**: 具体的な改善策の検討
4. **実行**: 改善策の実装
5. **効果測定**: 改善効果の確認

## リスク管理

### 主要リスクと対策

| リスク | 影響度 | 発生確率 | 対策 |
|--------|--------|----------|------|
| キーメンバーの離脱 | 高 | 低 | ドキュメント化、知識共有 |
| 技術的負債の蓄積 | 中 | 中 | 定期的なリファクタリング |
| セキュリティ脆弱性 | 高 | 低 | セキュリティレビュー、監査 |
| パフォーマンス劣化 | 中 | 中 | 監視、負荷テスト |
| 要件変更 | 中 | 高 | アジャイル開発、柔軟な設計 |

### 緊急時対応

#### インシデント対応フロー
1. **検知**: 問題の発見・報告
2. **評価**: 影響度・緊急度の判定
3. **対応**: 一次対応・復旧作業
4. **報告**: ステークホルダーへの状況報告
5. **分析**: 根本原因の調査
6. **改善**: 再発防止策の実装

## まとめ

効率的なプロジェクト管理により：
1. **品質向上**: 体系的な品質管理
2. **生産性向上**: 効率的なワークフロー
3. **リスク軽減**: 予防的なリスク管理
4. **チーム成長**: 継続的な学習と改善

このガイドラインに従い、持続可能で高品質なプロジェクト運営を実現しましょう。
