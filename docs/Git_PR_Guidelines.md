# Git・PR規約

## ブランチ命名
- 機能追加: feature/{ticket} (例: feature/JIRA-123)
- バグ修正: fix/{バグ内容}
- リファクタリング: refactor/{対象}
- その他メンテナンス: chore/{desc}

## コミットメッセージ
- プレフィックスを使用: feat:, fix:, docs:, style:, refactor:, test:, chore:
- 命令形で簡潔に記述する (例: "Add user authentication")
- 本文で詳細な説明を追加する

## PRプロセス
- 各PRは単一の機能または修正に焦点を当てる
- PRの説明には変更内容と目的を記述する
- PR内容は「PLAN→CODE→TEST→REPORT」セクションで構成
- コードレビューは少なくとも1人から承認を得る
- CI緑（lint.yml & tests.yml）を確認する