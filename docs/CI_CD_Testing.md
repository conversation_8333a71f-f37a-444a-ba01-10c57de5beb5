# CI/CD規約

## テスト規約
- Pestテストフレームワークを使用
- 単体テスト・機能テスト・統合テストを実装
- テストカバレッジを維持

## CI環境
- GitHub Actionsで自動化
- テスト: tests.yml - Pest、PHPUnit
- リンター: lint.yml - Laravel Pint
- Supabase: future playbook.yml

## 環境要件
- PHP 8.3.6
- Node.js 22.x
- Supabase CLI 2.22.12（固定バージョン）
  - GitHub Actionsのワークフローで指定
  - ローカル開発環境でも同一バージョンを使用

## デプロイフロー
1. feature/fix/refactorブランチ → develop PR
2. CI緑 + レビュー承認
3. develop → main (リリース準備完了時)
4. main → 本番デプロイ

## 環境設定
- 開発：.env.local 
- テスト：GitHub Actions環境変数
- 本番：セキュアな環境変数管理