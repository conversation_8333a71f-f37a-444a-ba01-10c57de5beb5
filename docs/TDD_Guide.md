# TDD実践ガイド
## テスト駆動開発による高品質なLaravel開発

### 概要
このガイドでは、JTT-Appsプロジェクトにおけるテスト駆動開発（TDD）の実践方法を詳しく説明します。

## TDDの基本原則

### Red-Green-Refactorサイクル
1. **Red（赤）**: 失敗するテストを書く
2. **Green（緑）**: テストを通す最小限の実装
3. **Refactor（リファクタ）**: テストが通る状態でコードを改善

### TDDの利点
- **品質向上**: バグの早期発見と修正
- **設計改善**: テスタブルなコードの自然な設計
- **リファクタリング安全性**: 既存機能の保護
- **ドキュメント効果**: テストが仕様書の役割

## 実践例：スタッフシフト管理機能

### 1. Red: 失敗するテストを書く

```php
<?php
// tests/Unit/StaffShiftServiceTest.php

use App\Services\StaffShiftService;
use App\Models\StaffShift;
use App\Exceptions\ShiftOverlapException;

describe('StaffShiftService', function () {
    beforeEach(function () {
        $this->service = new StaffShiftService();
    });

    it('creates a new staff shift', function () {
        $shiftData = [
            'staff_id' => 1,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
            'role_col' => 'cashier',
        ];

        $shift = $this->service->createShift($shiftData);

        expect($shift)->toBeInstanceOf(StaffShift::class);
        expect($shift->staff_id)->toBe(1);
        expect($shift->role_col)->toBe('cashier');
    });

    it('validates shift overlap', function () {
        // 既存シフト作成
        StaffShift::factory()->create([
            'staff_id' => 1,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
        ]);

        // 重複するシフトでエラーが発生することを確認
        expect(fn () => $this->service->createShift([
            'staff_id' => 1,
            'start_time' => '2025-01-01 10:00:00',
            'end_time' => '2025-01-01 16:00:00',
            'role_col' => 'cashier',
        ]))->toThrow(ShiftOverlapException::class);
    });

    it('calculates total working hours correctly', function () {
        $shift = StaffShift::factory()->make([
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
        ]);

        expect($shift->getTotalHours())->toBe(8.0);
    });
});
```

### 2. Green: 最小限の実装

```php
<?php
// app/Services/StaffShiftService.php

namespace App\Services;

use App\Models\StaffShift;
use App\Exceptions\ShiftOverlapException;

class StaffShiftService
{
    public function createShift(array $shiftData): StaffShift
    {
        // バリデーション
        $this->validateShiftOverlap($shiftData);

        // シフト作成
        return StaffShift::create($shiftData);
    }

    private function validateShiftOverlap(array $shiftData): void
    {
        $overlapping = StaffShift::where('staff_id', $shiftData['staff_id'])
            ->where(function ($query) use ($shiftData) {
                $query->whereBetween('start_time', [$shiftData['start_time'], $shiftData['end_time']])
                      ->orWhereBetween('end_time', [$shiftData['start_time'], $shiftData['end_time']])
                      ->orWhere(function ($q) use ($shiftData) {
                          $q->where('start_time', '<=', $shiftData['start_time'])
                            ->where('end_time', '>=', $shiftData['end_time']);
                      });
            })
            ->exists();

        if ($overlapping) {
            throw new ShiftOverlapException('シフトが重複しています。');
        }
    }
}
```

```php
<?php
// app/Models/StaffShift.php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

class StaffShift extends Model
{
    protected $fillable = [
        'staff_id',
        'start_time',
        'end_time',
        'role_col',
    ];

    protected $casts = [
        'start_time' => 'datetime',
        'end_time' => 'datetime',
    ];

    public function getTotalHours(): float
    {
        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);
        return $end->diffInHours($start);
    }
}
```

### 3. Refactor: コードの改善

```php
<?php
// app/Services/StaffShiftService.php（改善版）

namespace App\Services;

use App\Models\StaffShift;
use App\Exceptions\ShiftOverlapException;
use Illuminate\Support\Facades\DB;

class StaffShiftService
{
    public function createShift(array $shiftData): StaffShift
    {
        return DB::transaction(function () use ($shiftData) {
            // バリデーション
            $this->validateShiftData($shiftData);
            $this->validateShiftOverlap($shiftData);

            // シフト作成
            return StaffShift::create($shiftData);
        });
    }

    private function validateShiftData(array $shiftData): void
    {
        if (empty($shiftData['start_time']) || empty($shiftData['end_time'])) {
            throw new \InvalidArgumentException('開始時間と終了時間は必須です。');
        }

        $startTime = Carbon::parse($shiftData['start_time']);
        $endTime = Carbon::parse($shiftData['end_time']);

        if ($endTime->lte($startTime)) {
            throw new \InvalidArgumentException('終了時間は開始時間より後である必要があります。');
        }
    }

    private function validateShiftOverlap(array $shiftData): void
    {
        $overlapping = StaffShift::where('staff_id', $shiftData['staff_id'])
            ->where(function ($query) use ($shiftData) {
                $query->where(function ($q) use ($shiftData) {
                    // 開始時間が既存シフトの範囲内
                    $q->where('start_time', '<=', $shiftData['start_time'])
                      ->where('end_time', '>', $shiftData['start_time']);
                })->orWhere(function ($q) use ($shiftData) {
                    // 終了時間が既存シフトの範囲内
                    $q->where('start_time', '<', $shiftData['end_time'])
                      ->where('end_time', '>=', $shiftData['end_time']);
                })->orWhere(function ($q) use ($shiftData) {
                    // 新しいシフトが既存シフトを包含
                    $q->where('start_time', '>=', $shiftData['start_time'])
                      ->where('end_time', '<=', $shiftData['end_time']);
                });
            })
            ->exists();

        if ($overlapping) {
            throw new ShiftOverlapException('指定された時間帯に既存のシフトが存在します。');
        }
    }
}
```

## 機能テストの実装

```php
<?php
// tests/Feature/StaffShiftTest.php

use App\Models\User;
use App\Models\StaffShift;

describe('Staff Shift Management', function () {
    beforeEach(function () {
        $this->user = User::factory()->create();
        $this->actingAs($this->user);
    });

    it('allows authenticated user to create a shift', function () {
        $response = $this->postJson('/api/staff-shifts', [
            'staff_id' => $this->user->id,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
            'role_col' => 'cashier',
        ]);

        $response->assertStatus(201)
                 ->assertJsonStructure(['id', 'staff_id', 'start_time', 'end_time', 'role_col']);

        $this->assertDatabaseHas('staff_shifts', [
            'staff_id' => $this->user->id,
            'role_col' => 'cashier',
        ]);
    });

    it('validates required fields', function () {
        $response = $this->postJson('/api/staff-shifts', []);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['staff_id', 'start_time', 'end_time', 'role_col']);
    });

    it('prevents overlapping shifts', function () {
        // 既存シフト作成
        StaffShift::factory()->create([
            'staff_id' => $this->user->id,
            'start_time' => '2025-01-01 09:00:00',
            'end_time' => '2025-01-01 17:00:00',
        ]);

        // 重複するシフト作成を試行
        $response = $this->postJson('/api/staff-shifts', [
            'staff_id' => $this->user->id,
            'start_time' => '2025-01-01 10:00:00',
            'end_time' => '2025-01-01 16:00:00',
            'role_col' => 'cashier',
        ]);

        $response->assertStatus(422)
                 ->assertJsonValidationErrors(['start_time']);
    });
});
```

## TDDベストプラクティス

### 1. テスト命名規則
- **日本語での説明的な名前**: `it('calculates total working hours correctly')`
- **振る舞いベース**: 何をテストするかを明確に

### 2. テスト構造（AAA パターン）
- **Arrange（準備）**: テストデータの準備
- **Act（実行）**: テスト対象の実行
- **Assert（検証）**: 結果の検証

### 3. テストの独立性
- 各テストは他のテストに依存しない
- `beforeEach` でセットアップ、`afterEach` でクリーンアップ

### 4. モックの活用
```php
it('sends notification on shift creation', function () {
    $notificationService = Mockery::mock(NotificationService::class);
    $notificationService->shouldReceive('sendShiftCreationNotification')
        ->once()
        ->andReturn(true);

    $service = new StaffShiftService($notificationService);
    $shift = $service->createShift([/* データ */]);

    // モックの呼び出しは自動的に検証される
});
```

## テスト実行とカバレッジ

### 基本コマンド
```bash
# 全テスト実行
php artisan test

# 特定テスト実行
php artisan test tests/Unit/StaffShiftServiceTest.php

# カバレッジ付きテスト
php artisan test --coverage

# 並列テスト実行
php artisan test --parallel
```

### カバレッジ目標
- **Service層**: 100%
- **Model層**: 90%
- **Controller層**: 80%

## まとめ

TDDを実践することで：
1. **品質の高いコード**: バグの少ない実装
2. **保守しやすい設計**: テスタブルなアーキテクチャ
3. **安全なリファクタリング**: 既存機能の保護
4. **明確な仕様**: テストが仕様書の役割

常にRed-Green-Refactorサイクルを意識し、テストファーストで開発を進めましょう。
