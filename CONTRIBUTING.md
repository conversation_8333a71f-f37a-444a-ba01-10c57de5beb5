# Contributing Guide

## プロジェクトへの貢献方法

このドキュメントでは、JTT-Appsプロジェクトへの貢献方法を説明します。

## 基本ルール

### 1. Issue駆動開発の徹底

**🎯 このプロジェクトは全てGitHub IssuesでTODOを管理します。**

**すべての変更は必ずGitHub Issueから始めてください。**

- 新機能追加 → Issueを作成
- バグ修正 → Issueを作成
- リファクタリング → Issueを作成
- ドキュメント更新 → Issueを作成
- 要件定義 → Issueを作成
- フェーズ計画 → Issueを作成
- プロジェクト管理タスク → Issueを作成

### 2. Issue作成ガイドライン

#### タイトル形式
```
[種別] 簡潔な説明
```

#### 種別一覧（タイトルプレフィックス）
- `[feat]` - 新機能追加
- `[bug]` - バグ修正
- `[refactor]` - リファクタリング
- `[docs]` - ドキュメント更新
- `[test]` - テスト追加・修正
- `[ci]` - CI/CD関連

#### Issue本文テンプレート
```markdown
## 背景
なぜこの変更が必要か

## 要件
- 要件1
- 要件2

## 期待する結果
変更後の動作・効果

## 技術的詳細
- 使用技術
- 実装方針
- 考慮事項
```

### 3. ブランチ戦略（シンプルなGitHubフロー）

```
main
  ↑
feature/issue-<番号>-<簡潔な説明>
fix/issue-<番号>-<簡潔な説明>
refactor/issue-<番号>-<簡潔な説明>
```

例:
- `feature/issue-123-staff-shift-management`
- `fix/issue-456-login-validation`
- `refactor/issue-789-user-service`

### 4. TDD（テスト駆動開発）ワークフロー

**すべての開発はTDDで行います。実装前に必ずテストを書いてください。**

> 📖 **詳細情報**: [docs/TDD_Guide.md](./docs/TDD_Guide.md) - TDD実践の詳細手順

#### TDDの基本ステップ（要約）
1. **Red（赤）**: 失敗するテストを書く
2. **Green（緑）**: テストを通す最小限の実装
3. **Refactor（リファクタ）**: コードを改善

#### 開発フロー例（要約）
```bash
# 1. Issue確認 → ブランチ作成
git checkout -b feature/issue-123-staff-shift-management

# 2. TDDサイクル実行
# Red → Green → Refactor

# 3. 品質チェック
php artisan test && php artisan dusk
./vendor/bin/pint && ./vendor/bin/phpstan analyse

# 4. コミット・PR作成
git commit -m "feat: スタッフシフト管理機能を追加"
```

> 📖 **詳細情報**: [docs/Development_Workflow_Guide.md](./docs/Development_Workflow_Guide.md) - 完全な開発ワークフロー

### 5. Pull Request作成

> 📖 **詳細情報**: [docs/Git_PR_Guidelines.md](./docs/Git_PR_Guidelines.md) - Git・PR規約詳細

#### 必須事項（要約）
1. **Issue紐付け**: 本文に `Closes #<issue番号>` を含める
2. **レビュー依頼**: 適切なレビュアーを設定
3. **CI通過**: すべてのチェックがグリーン

#### PRタイトル形式
```
<type>(<scope>): <説明> (#<issue番号>)
```

### 6. 品質基準・禁止事項

> 📖 **詳細情報**: [docs/CI_CD_Testing.md](./docs/CI_CD_Testing.md) - 品質基準とテスト戦略

#### 必須チェック項目（要約）
- [ ] テストが通ること（`php artisan test` + `php artisan dusk`）
- [ ] コードスタイルが適切であること（Laravel Pint）
- [ ] 静的解析でエラーがないこと（PHPStan）
- [ ] セキュリティ要件を満たしていること

#### 禁止事項
❌ **絶対にやってはいけないこと**:
- Issueなしの直接PR作成
- テストなしの実装
- コードスタイルチェックを通さない
- 静的解析エラーを無視する

### 7. トラブルシューティング

#### よくある問題
- **PRがマージされない**: Issue紐付け、CI通過、レビュー承認を確認
- **テストが失敗する**: 環境設定、Supabase起動、マイグレーション実行を確認

> 📖 **詳細情報**: [docs/Project_Management_Guide.md](./docs/Project_Management_Guide.md) - プロジェクト管理とトラブルシューティング

## 参考リンク

### 🎯 新規参画者向け学習パス
1. **プロジェクト理解**: [README.md](./README.md) → [CONTRIBUTING.md](./CONTRIBUTING.md)
2. **開発プロセス**: [docs/Development_Workflow_Guide.md](./docs/Development_Workflow_Guide.md) → [docs/TDD_Guide.md](./docs/TDD_Guide.md)
3. **技術仕様**: [docs/CodingStandards.md](./docs/CodingStandards.md) → [docs/Security_Guidelines.md](./docs/Security_Guidelines.md)
4. **実践開始**: Issue作成 → ブランチ作成 → TDD実装

### 📚 マスタードキュメント
- **[AGENTS.md](./AGENTS.md)** - AIエージェント向け統合ガイド
- **[docs/Documentation_Structure_Guide.md](./docs/Documentation_Structure_Guide.md)** - ドキュメント構造ガイド

> 📖 **重要**: 情報の詳細は各マスタードキュメントを参照してください。このファイルは要約と参照のみを提供します。
