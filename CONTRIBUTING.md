# Contributing Guide

## プロジェクトへの貢献方法

このドキュメントでは、JTT-Appsプロジェクトへの貢献方法を説明します。

## 基本ルール

### 1. Issue駆動開発の徹底

**🎯 このプロジェクトは全てGitHub IssuesでTODOを管理します。**

**すべての変更は必ずGitHub Issueから始めてください。**

- 新機能追加 → Issueを作成
- バグ修正 → Issueを作成
- リファクタリング → Issueを作成
- ドキュメント更新 → Issueを作成
- 要件定義 → Issueを作成
- フェーズ計画 → Issueを作成
- プロジェクト管理タスク → Issueを作成

### 2. Issue作成ガイドライン

#### タイトル形式
```
[種別] 簡潔な説明
```

#### 種別一覧（タイトルプレフィックス）
- `[feat]` - 新機能追加
- `[bug]` - バグ修正
- `[refactor]` - リファクタリング
- `[docs]` - ドキュメント更新
- `[test]` - テスト追加・修正
- `[ci]` - CI/CD関連

#### Issue本文テンプレート
```markdown
## 背景
なぜこの変更が必要か

## 要件
- 要件1
- 要件2

## 期待する結果
変更後の動作・効果

## 技術的詳細
- 使用技術
- 実装方針
- 考慮事項
```

### 3. ブランチ戦略（シンプルなGitHubフロー）

```
main
  ↑
feature/issue-<番号>-<簡潔な説明>
fix/issue-<番号>-<簡潔な説明>
refactor/issue-<番号>-<簡潔な説明>
```

例:
- `feature/issue-123-staff-shift-management`
- `fix/issue-456-login-validation`
- `refactor/issue-789-user-service`

### 4. TDD（テスト駆動開発）ワークフロー

**すべての開発はTDDで行います。実装前に必ずテストを書いてください。**

#### TDDの基本ステップ

1. **Red（赤）**: 失敗するテストを書く
```php
// ❌ この時点では実装がないので失敗する
it('calculates total working hours correctly', function () {
    $shift = new StaffShift([
        'start_time' => '2025-01-01 09:00:00',
        'end_time' => '2025-01-01 17:00:00',
    ]);

    expect($shift->getTotalHours())->toBe(8.0);
});
```

2. **Green（緑）**: テストを通す最小限の実装
```php
// ✅ テストが通る最小限の実装
class StaffShift extends Model
{
    public function getTotalHours(): float
    {
        $start = Carbon::parse($this->start_time);
        $end = Carbon::parse($this->end_time);
        return $end->diffInHours($start);
    }
}
```

3. **Refactor（リファクタ）**: コードを改善
```php
// ✨ バリデーションやエラーハンドリングを追加
public function getTotalHours(): float
{
    if (!$this->start_time || !$this->end_time) {
        throw new InvalidArgumentException('開始時間と終了時間が必要です');
    }

    $start = Carbon::parse($this->start_time);
    $end = Carbon::parse($this->end_time);

    if ($end->lte($start)) {
        throw new InvalidArgumentException('終了時間は開始時間より後である必要があります');
    }

    return $end->diffInHours($start);
}
```

#### 開発フロー例

```bash
# 1. Issueを確認
# 2. ブランチを作成
git checkout -b feature/issue-123-staff-shift-management

# 3. テストファイルを作成
touch tests/Unit/StaffShiftServiceTest.php

# 4. 失敗するテストを書く
php artisan test tests/Unit/StaffShiftServiceTest.php

# 5. 実装ファイルを作成して最小限の実装
touch app/Services/StaffShiftService.php

# 6. テストが通ることを確認

# 7. リファクタリング

# 8. すべてのテストが通ることを確認
php artisan test

# 9. コードスタイルチェック
./vendor/bin/pint

# 10. 静的解析
./vendor/bin/phpstan analyse

# 11. コミット
git add .
git commit -m "feat: スタッフシフト管理機能を追加"
```

#### テスト作成のルール

- **1機能1テスト**: 各テストは1つの振る舞いだけを検証
- **分かりやすい名前**: 日本語で仕様が分かる名前にする
- **AAA パターン**: Arrange（準備）→ Act（実行）→ Assert（検証）

### 5. Pull Request作成

#### 必須事項
1. **Issue紐付け**: 本文に `Closes #<issue番号>` を含める
2. **レビュー依頼**: 適切なレビュアーを設定
3. **CI通過**: すべてのチェックがグリーン

#### PRタイトル形式
```
<type>(<scope>): <説明> (#<issue番号>)
```

例:
- `feat(service): スタッフシフト管理機能を追加 (#123)`
- `fix(auth): ログインバリデーションの不具合を修正 (#456)`

#### PRテンプレート
```markdown
## 概要
Closes #<issue番号>

## 変更内容
- 変更点1
- 変更点2

## テスト
- 単体テスト: StaffShiftServiceTest
- 機能テスト: StaffShiftTest
- カバレッジ: 95%以上

## 確認事項
- [ ] テストが通ること
- [ ] コードスタイルが適切であること
- [ ] 静的解析でエラーがないこと
- [ ] ドキュメントが更新されていること
```

### 6. コミットメッセージ

[Conventional Commits](https://www.conventionalcommits.org/)に従う:

```
<type>(<scope>): <説明>

<本文>（任意）

<フッター>（任意）
```

#### Type一覧
- `feat`: 新機能追加
- `fix`: バグ修正
- `docs`: ドキュメント更新
- `style`: コードスタイル修正
- `refactor`: リファクタリング
- `test`: テスト追加・修正
- `chore`: その他のメンテナンス

### 7. コードレビュー

- **人間レビュー**: アーキテクチャ・ビジネスロジック確認
- **自動チェック**: lint, typecheck, test, build

### 8. 品質基準

#### 必須チェック項目
- [ ] テストが通ること
- [ ] コードスタイルが適切であること（Laravel Pint）
- [ ] 静的解析でエラーがないこと（PHPStan）
- [ ] セキュリティ要件を満たしていること
- [ ] ドキュメントが更新されていること

#### カバレッジ目標
| レイヤー | 目標 | 優先度 |
|---------|------|--------|
| Service層 | 100% | 高 |
| Model層 | 90% | 高 |
| Controller層 | 80% | 中 |

### 9. 禁止事項

❌ **絶対にやってはいけないこと**:
- Issueなしの直接PR作成
- テストなしの実装
- コードスタイルチェックを通さない
- 静的解析エラーを無視する
- セキュリティ要件を無視する

### 10. トラブルシューティング

#### Q: PRがマージされない
A: 以下を確認してください
- Issueと紐付いているか
- CIがすべて通っているか
- レビューが承認されているか

#### Q: テストが失敗する
A: 以下を確認してください
- 環境設定が正しいか
- Supabaseが起動しているか
- マイグレーションが実行されているか

## 参考リンク

- [AGENTS.md](./AGENTS.md) - AIエージェント向け詳細ガイド
- [開発ワークフローガイド](./docs/Development_Workflow_Guide.md)
- [コーディング規約](./docs/CodingStandards.md)
- [View実装ガイドライン](./docs/View_Implementation_Guidelines.md)
- [CI/CD・テスト規約](./docs/CI_CD_Testing.md)
