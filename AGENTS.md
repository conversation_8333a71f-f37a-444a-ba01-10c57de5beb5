# JTT-Apps プロジェクト AIエージェント向け標準ガイド

> **目的**: すべての **AI エージェントと新規開発者** が "迷わず・安全に・バグなく" **Laravel風アーキテクチャ** で実装できる唯一の開発リファレンス

## 目次

1. [開発ワークフローガイド](./docs/Development_Workflow_Guide.md) - TDD実践とバグ防止プロセス
2. [コーディング規約](./docs/CodingStandards.md) - Laravel風の開発規約
3. [View実装ガイドライン](./docs/View_Implementation_Guidelines.md) - セキュアなView開発
4. [Git・PR規約](./docs/Git_PR_Guidelines.md) - シンプルなGitHubフロー
5. [CI/CD・テスト規約](./docs/CI_CD_Testing.md) - TDD実践とテスト戦略
6. [TDDガイド](./docs/TDD_Guide.md) - テスト駆動開発の実践
7. [Laravel Dusk E2Eテストガイド](./docs/Laravel_Dusk_E2E_Testing.md) - ブラウザテスト実践
8. [セキュリティガイドライン](./docs/Security_Guidelines.md) - セキュリティ対策
9. [プロジェクト管理ガイド](./docs/Project_Management_Guide.md) - 効率的なプロジェクト管理
10. [Laravel × Supabase統合](./docs/Laravel_Supabase_Integration.md) - 統合アプローチ
11. [Supabaseガイドライン](./docs/Supabase_Guidelines.md) - データベース設計
12. [マイグレーション規約](./docs/Laravel_Migration_Convention.md) - スキーマ管理

## プロジェクト概要

### 🎯 プロジェクト目標
高品質で保守性の高いWebアプリケーションを、TDD、適切なGitフロー、CI/CDパイプラインを通じて開発する。

### 🛠 技術スタック
- **Backend**: Laravel 12.13.0 (PHP 8.3.6)
- **Frontend**: Livewire + Flux UI / Inertia.js + Vue
- **Database**: Supabase (PostgreSQL)
- **Testing**: Pest (PHPUnit)
- **CI/CD**: GitHub Actions
- **Code Quality**: Laravel Pint, PHPStan

### 📋 環境要件
- PHP 8.3.6
- Node.js 22.x
- Supabase CLI 2.22.12（固定バージョン）
- Composer
- Git

## 重要原則

### プロジェクト管理原則
- **🎯 GitHub Issues管理**: このプロジェクトは全てGitHub IssuesでTODOを管理します
- **Issue駆動開発**: すべての変更はIssueから開始
- **要件定義もIssue化**: フェーズ計画、要件定義もすべてIssueとして管理

### アーキテクチャ原則
- **レイヤードアーキテクチャ**: `Controller → Service → Model → DB` の依存方向を必ず守る
- **単一責任の原則**: 各クラスは一つの責任のみを持つ
- **依存性注入**: DIコンテナを活用した疎結合設計
- **テストファースト**: TDDによる品質確保

### セキュリティ原則
- **XSS対策**: Bladeテンプレートでの適切なエスケープ
- **CSRF保護**: 全フォームでCSRFトークン必須
- **認可チェック**: Policyを使用した適切な権限管理
- **入力検証**: FormRequestによる厳密なバリデーション

### データベース原則
- **Supabase統合**: Laravel Migration + Supabase Migration の同期
- **RLS適用**: Row Level Securityによるデータ保護
- **API経由アクセス**: `public.*_api` 関数経由でのデータアクセス
- **予約語回避**: `*_col` サフィックスによる予約語回避

## AIエージェント役割分担

| AIエージェント | 主な役割 | 責任範囲 |
|-----------|--------|--------|
| **Claude (Augment Agent)** | アーキテクチャ設計、コードレビュー、品質保証 | 設計文書の保守、レビュー基準適用、品質チェック |
| **GitHub Copilot** | コード補完、実装支援 | 開発効率向上、コーディング支援 |
| **その他AIツール** | 特定タスク支援 | ドキュメント生成、テスト作成支援 |

## 開発フロー

### 1. 基本ワークフロー
```bash
# 1. 機能ブランチ作成
git checkout -b feature/機能名

# 2. TDD実践
# Red → Green → Refactor

# 3. テスト実行
php artisan test

# 4. コード品質チェック
./vendor/bin/pint && ./vendor/bin/phpstan analyse

# 5. PR作成
git push origin feature/機能名

# 6. CI通過 + レビュー承認
# 7. mainブランチへマージ
```

### 2. TDD（テスト駆動開発）サイクル
1. **Red**: 失敗するテストを先に書く
2. **Green**: テストを通す最小限の実装
3. **Refactor**: テストが通る状態でコードを改善

### 3. 必須チェック項目
- [ ] テストが通ること
- [ ] コードスタイルが適切であること（Laravel Pint）
- [ ] 静的解析でエラーがないこと（PHPStan）
- [ ] セキュリティ要件を満たしていること
- [ ] ドキュメントが更新されていること

## コーディング規約（概要）

### PHP規約
- **スタイル**: PSR-12準拠
- **型宣言**: `declare(strict_types=1);` 必須
- **命名規則**:
  - クラス: PascalCase (`UserService`)
  - メソッド: camelCase (`createUser()`)
  - 変数: camelCase (`$userName`)
  - 定数: SNAKE_CASE_CAPS (`USER_ROLES`)

### Laravel規約
- **Migration**: `YYYYMMDDHHMM_<desc>.php` 形式
- **テーブル名**: snake_case複数形
- **予約語回避**: `*_col` サフィックス使用
- **Eloquent**: Active Recordパターン活用

### Supabase規約
- **スキーマ分離**: ビジネステーブル → `core.*`、公開API → `public.*_api`
- **関数引数**: `p_` プレフィックス
- **RLS**: 適切なRow Level Security設定
- **マイグレーション**: `supabase/migrations/YYYYMMDDHHMM_<desc>.sql`

## テスト戦略

### テストピラミッド
- **単体テスト（70%）**: Service層の完全カバー
- **統合テスト（20%）**: 複数コンポーネントの連携
- **E2Eテスト（10%）**: Laravel Duskによるブラウザテスト

### カバレッジ目標
| レイヤー | 目標 | 優先度 |
|---------|------|--------|
| Service層 | 100% | 高 |
| Model層 | 90% | 高 |
| Controller層 | 80% | 中 |
| View層 | 70% | 中 |

### テスト実行コマンド
```bash
# 全テスト実行
php artisan test

# E2Eテスト実行（Laravel Dusk）
php artisan dusk

# カバレッジ付きテスト
php artisan test --coverage

# 並列テスト実行
php artisan test --parallel

# 特定テスト実行
php artisan test tests/Feature/StaffShiftTest.php
```

## CI/CD パイプライン

### GitHub Actions ワークフロー
- **tests.yml**: Pest、PHPUnit実行
- **lint.yml**: Laravel Pint実行
- **phpstan.yml**: 静的解析実行
- **dusk.yml**: Laravel Dusk E2Eテスト実行

### 品質ゲート
1. 全テストが通ること
2. コードスタイルチェックが通ること
3. 静的解析でエラーがないこと
4. セキュリティスキャンが通ること

## PR管理

### PR作成ルール
- **命名**: `type(scope): 説明`
- **説明**: 概要、変更内容、テスト、確認事項を記載
- **レビュー**: 最低1人の承認が必要
- **CI**: 全チェックが緑になること

### PR構成テンプレート
```markdown
## 概要
変更内容の簡潔な説明

## 変更内容
- 具体的な変更点をリスト形式で記載

## テスト
- 追加・修正したテストの説明
- 手動テストの手順（必要に応じて）

## 確認事項
- [ ] テストが通ること
- [ ] コードスタイルが適切であること
- [ ] 静的解析でエラーがないこと
- [ ] ドキュメントが更新されていること
```

## セキュリティガイドライン

### 必須セキュリティ対策
1. **入力検証**: FormRequestによる厳密なバリデーション
2. **出力エスケープ**: Bladeテンプレートでの適切なエスケープ
3. **CSRF保護**: 全フォームでトークン検証
4. **認証・認可**: Policyによる適切な権限管理
5. **SQLインジェクション対策**: Eloquent ORM使用

### セキュリティチェックリスト
- [ ] ユーザー入力は適切にバリデーションされているか
- [ ] 出力は適切にエスケープされているか
- [ ] CSRFトークンが設定されているか
- [ ] 認可チェックが実装されているか
- [ ] 機密情報がログに出力されていないか

## 運用ルール

### ドキュメント管理
- **唯一の情報源**: このドキュメント群が開発基準の唯一の情報源
- **変更管理**: Issue作成→合意→PR→マージのフローを遵守
- **不明確な点**: 「【不明確】」と明示し、推測実装は避ける

### 品質確保
- **コードレビュー**: 必ずチェックリストに沿って確認
- **テスト**: 最低1ケース以上の実装
- **リファクタリング**: テスト作成後に実施
- **依存方向**: アーキテクチャ原則の厳守

## AIエージェント連携方法

### 情報共有
- 重要な変更は他のAIエージェントに明示的に通知
- 関連するドキュメントへの参照を常に含める
- 不明確な点は「【不明確】」と明記し、推測は避ける

### タスク引き継ぎ
タスクを他のAIエージェントに引き継ぐ際は、以下の情報を必ず共有:

1. 現在の進捗状況
2. 実装済みの機能と残タスク
3. 直面している課題やブロッカー
4. 関連する Issue や PR の番号
5. 参照すべきドキュメントやコード

---

> **重要**: このガイドラインに従うことで、チーム全体でバグの少ない高品質なLaravelアプリケーションを開発できます。不明な点があれば、必ずドキュメントを参照し、推測での実装は避けてください。
