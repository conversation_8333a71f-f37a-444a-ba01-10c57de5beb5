# JTT アプリケーション
## バグを防ぐLaravel + Supabase開発プロジェクト

### 🎯 プロジェクト目標
高品質で保守性の高いWebアプリケーションを、TDD、適切なGitフロー、CI/CDパイプラインを通じて開発する。

### 🛠 技術スタック
- **Backend**: Laravel 12.13.0 (PHP 8.3.6)
- **Frontend**: Livewire + Flux UI / Inertia.js + Vue
- **Database**: Supabase (PostgreSQL)
- **Testing**: Pest (PHPUnit)
- **CI/CD**: GitHub Actions
- **Code Quality**: Laravel Pint, PHPStan

### 📋 環境要件
- PHP 8.3.6
- Node.js 22.x
- Supabase CLI 2.22.12（固定バージョン）
- Composer
- Git

### 🚀 クイックスタート
```bash
# 1. リポジトリクローン
git clone https://github.com/ShintaroKawakami/jtt-apps.git
cd jtt-apps

# 2. 依存関係インストール
composer install
npm install

# 3. 環境設定
cp .env.example .env.local
php artisan key:generate

# 4. Supabase起動
supabase start

# 5. データベースセットアップ
php artisan migrate

# 6. 開発サーバー起動
php artisan serve
npm run dev
```

### 📚 開発ガイドライン

#### 🔧 基本ドキュメント
- **[開発ワークフローガイド](docs/Development_Workflow_Guide.md)** - TDD実践とバグ防止プロセス
- **[コーディング規約](docs/CodingStandards.md)** - Laravel風の開発規約
- **[View実装ガイドライン](docs/View_Implementation_Guidelines.md)** - セキュアなView開発
- **[Git・PR規約](docs/Git_PR_Guidelines.md)** - シンプルなGitHubフロー
- **[CI/CD・テスト規約](docs/CI_CD_Testing.md)** - TDD実践とテスト戦略

#### 🗄 データベース・インフラ
- **[Laravel × Supabase統合](docs/Laravel_Supabase_Integration.md)** - 統合アプローチ
- **[Supabaseガイドライン](docs/Supabase_Guidelines.md)** - データベース設計
- **[マイグレーション規約](docs/Laravel_Migration_Convention.md)** - スキーマ管理

#### 📖 詳細マニュアル
- **[Laravel開発マニュアル](docs/laravel_manual/controller_service_model_manual.html)** - アーキテクチャガイド
- **[アーキテクチャガイド](docs/laravel_manual/architecture_guide.html)** - レイヤード設計
- **[テスト戦略ガイド](docs/laravel_manual/testing_guide.html)** - 包括的テスト手法
- **[Laravel Dusk E2Eテストガイド](docs/Laravel_Dusk_E2E_Testing.md)** - ブラウザテスト実践

### 🔄 開発フロー
1. **機能ブランチ作成**: `git checkout -b feature/機能名`
2. **TDD実践**: Red → Green → Refactor
3. **テスト実行**: `php artisan test`
4. **コード品質チェック**: `./vendor/bin/pint && ./vendor/bin/phpstan analyse`
5. **PR作成**: mainブランチへ直接PR
6. **CI通過**: GitHub Actions
7. **コードレビュー**: 承認後マージ

### 🧪 テスト実行
```bash
# 全テスト実行
php artisan test

# E2Eテスト実行（Laravel Dusk）
php artisan dusk

# カバレッジ付きテスト
php artisan test --coverage

# 特定テスト実行
php artisan test tests/Feature/StaffShiftTest.php

# 並列テスト実行
php artisan test --parallel
```

### 🔍 コード品質チェック
```bash
# コードスタイル修正
./vendor/bin/pint

# 静的解析
./vendor/bin/phpstan analyse

# 型チェック（レベル8）
./vendor/bin/phpstan analyse --level=8
```

### 📁 プロジェクト構造
```
jtt-apps/
├── app/
│   ├── Http/Controllers/     # コントローラー
│   ├── Services/            # ビジネスロジック
│   ├── Models/              # Eloquentモデル
│   └── Livewire/            # Livewireコンポーネント
├── docs/                    # プロジェクトドキュメント
├── tests/
│   ├── Feature/             # 機能テスト
│   └── Unit/                # 単体テスト
├── resources/views/         # Bladeテンプレート
└── supabase/               # Supabaseマイグレーション
```

### 🤝 コントリビューション
1. Issueを作成して機能や修正を提案
2. 機能ブランチを作成
3. TDDでコードを実装
4. テストとコード品質チェックを実行
5. PRを作成してレビューを依頼

### 📞 サポート
- **ドキュメント**: `docs/` フォルダ内の各種ガイド
- **Issue**: GitHubのIssue機能を活用
- **コードレビュー**: PR作成時に自動的にレビュー依頼
