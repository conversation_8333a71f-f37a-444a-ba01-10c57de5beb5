# JTT アプリケーション
- [Laravel × Supabase 開発マニュアル](docs/laravel_manual/controller_service_model_manual.html)
- [Coding Standards](docs/CodingStandards.md)

## 環境要件
- PHP 8.3.6
- Node.js 22
- Supabase CLI 2.22.12（固定バージョン）

## ドキュメント一覧
- [コーディング規約](docs/CodingStandards.md)
- [CI/CD & テスト](docs/CI_CD_Testing.md)
- [Supabase ガイドライン](docs/Supabase_Guidelines.md)
- [Laravel × Supabase 開発マニュアル](docs/laravel_manual/controller_service_model_manual.html)
- その他は docs/ フォルダを参照
