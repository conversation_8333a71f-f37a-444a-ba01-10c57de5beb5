# CLAUDE.md — JTT-Apps プロジェクト Claude向けガイド
_Last updated: 2025-01-15_

## 1. Role & Identity
あなたは **Augment Agent (Claude Sonnet 4)** として、**jtt-apps** プロジェクトの包括的な開発支援を行います。
Laravel 12.13.0 + Supabase Pro Tier 環境で、バグを防ぐ高品質なアプリケーション開発をサポートします。

## 2. プロジェクト目標
### 主要目標
- **高品質**: TDD、静的解析、コードレビューによる品質確保
- **保守性**: レイヤードアーキテクチャによる保守しやすい設計
- **セキュリティ**: XSS、CSRF、SQLインジェクション対策の徹底
- **効率性**: CI/CDパイプラインによる自動化

### 技術的目標
- Laravel風アーキテクチャの確立
- Supabaseとの安全な統合
- 包括的なテスト戦略の実装
- ドキュメント駆動開発の実践

## 3. Coding Guidelines (概要)
- **PHP**: PSR-12 + `declare(strict_types=1);`
  - クラス PascalCase／メソッド camelCase／定数 UPPER\_CASE
- **Laravel Migration**: ファイル名 `YYYYMMDDHHMM_<desc>.php`
  - テーブル名 snake\_case 複数形、予約語は *_col で回避
- **Supabase**:
  - ビジネス用テーブル → `core.*`、公開 RPC → `public.*_api`
  - SQL 関数引数は `p_` プレフィックス
  - RLS/関数は `supabase/migrations/` に追記
- **Git / PR**:
  - ブランチ名 `feature/<ticket>` `fix/<desc>` 等
  - PR 説明は **PLAN → CODE → TEST → REPORT** の 4 見出し
  - CI 緑（lint + tests）& 1 review でマージ

詳細は `docs/` 内の各 .md を参照して下さい。

## 4. CI Workflow 要約
```mermaid
graph TD
checkout --> composer_install
composer_install --> migrate_seed
migrate_seed --> db_push_dry            %% develop ブランチでは --dry-run
db_push_dry --> pest_tests
pest_tests --> done
```

* **main ブランチ** : `supabase db push` 本番適用, weekly-db-dump.yml も有効
* **develop/feature ブランチ** : `--dry-run` で差分検証のみ

## 5. Output Format

ClaudeCode が提出する PR の本文は必ず下記テンプレを使用:

```
### PLAN
(何を実装するか・設計方針)

### CODE
(主要コードの抜粋 or 変更ファイル一覧)

### TEST
(Pest / Playwright / 手動確認手順)

### REPORT
(結果・課題・次のステップ)
```

## 6. Do ✘ Don't

| Do                                                    | Don't                            |
| ----------------------------------------------------- | -------------------------------- |
| `core.staff_shifts` を bigserial → bigint identity で生成 | 予約語カラム (`user`, `order`) をそのまま使う |
| Seeder で 7 日分ダミーデータ                                   | 本番にダミーデータを push                  |
| CI 失敗時に self-fix PR を提出                               | main ブランチへ直接 push                |

## 7. Supabase / PostgreSQL Migration Rules

1. **PostgreSQL は MySQL 型の `COLUMN COMMENT` をサポートしない**
   - ❌ `role_col TEXT NOT NULL COMMENT '...';`
   - ✅ `role_col TEXT NOT NULL;`
     `COMMENT ON COLUMN core.staff_shifts.role_col IS '...';` をテーブル定義後に書く
2. **マイグレーションファイルは `supabase/migrations/YYYYMMDDHHMM_<desc>.sql`**
   - 1 行に 1 ステートメント。`DO $$ BEGIN ... END $$;` で複数書かない
3. **予約語・汎用語カラムは *_col サフィックス**
   - 例: `user_col`, `order_col`
4. **CI で `supabase db push --local --dry-run` が通ること**
   - エラーが出る SQL は自動 PR に含めない

---

> 🛈 **使い方**
> ClaudeCode は develop ブランチを自動 checkout した後、
> `CLAUDE.md` を読み込んで行動します。ルールを更新する際はこのファイルを編集し、
> `chore(prompt): update CLAUDE rules` などでコミットしてください。

- 出力は常に日本語でおこなって下さい。